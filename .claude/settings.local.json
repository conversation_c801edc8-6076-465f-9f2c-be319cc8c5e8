{"permissions": {"allow": ["Bash(find:*)", "<PERSON><PERSON>(python:*)", "Bash(rm:*)", "Bash(grep:*)", "Bash(rg:*)", "<PERSON><PERSON>(echo:*)", "<PERSON><PERSON>(sed:*)", "Bash(awk:*)", "<PERSON><PERSON>(timeout 10s uvicorn:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(uvicorn:*)", "Bash(kill:*)", "Bash(lsof:*)", "mcp__ide__getDiagnostics", "<PERSON><PERSON>(true)"], "deny": []}}